'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  FileText,
  Award,
  Download,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Star
} from 'lucide-react';

interface TestResult {
  id: string;
  listeningBandScore: number;
  readingBandScore: number;
  writingBandScore: number;
  speakingBandScore: number;
  overallBandScore: number;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial?: string;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Candidate {
  id: string;
  candidateNumber: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  testDate: string;
  testCenter: string;
  photoUrl?: string;
  createdAt: string;
  updatedAt: string;
  testResults: TestResult[];
}

export default function CandidateDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const candidateId = params.id as string;
  
  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (candidateId) {
      fetchCandidateDetails();
    }
  }, [candidateId]);

  const fetchCandidateDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/candidates/${candidateId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch candidate details');
      }
      
      const data = await response.json();
      setCandidate(data);
    } catch (error) {
      console.error('Error fetching candidate details:', error);
      setError('Failed to load candidate details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'verified':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBandScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 font-bold';
    if (score >= 6.5) return 'text-blue-600 font-semibold';
    if (score >= 5.5) return 'text-yellow-600 font-medium';
    return 'text-red-600 font-medium';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !candidate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Link
            href="/admin/candidates"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Candidate Details</h1>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error || 'Candidate not found'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/candidates"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Candidate Details</h1>
            <p className="text-gray-600">Complete candidate information and test history</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link
            href={`/admin/candidates/${candidateId}/edit`}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Edit Candidate
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Candidate Information */}
        <div className="lg:col-span-1 space-y-6">
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <User className="h-5 w-5 mr-2" />
              Personal Information
            </h2>
            
            <div className="flex items-start space-x-4 mb-6">
              {candidate.photoUrl ? (
                <Image
                  src={candidate.photoUrl}
                  alt={candidate.fullName}
                  width={80}
                  height={80}
                  className="rounded-lg object-cover"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                  <User className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{candidate.fullName}</h3>
                <p className="text-gray-600">#{candidate.candidateNumber}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center text-sm text-gray-600">
                <Mail className="h-4 w-4 mr-3" />
                <span>{candidate.email}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-3" />
                <span>{candidate.phoneNumber}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-3" />
                <span>Born: {new Date(candidate.dateOfBirth).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-3" />
                <span>{candidate.nationality}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <FileText className="h-4 w-4 mr-3" />
                <span>Passport: {candidate.passportNumber}</span>
              </div>
            </div>
          </div>

          {/* Test Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Test Information
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-3" />
                <span>Test Date: {new Date(candidate.testDate).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-3" />
                <span>Test Center: {candidate.testCenter}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-3" />
                <span>Registered: {new Date(candidate.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Test Results History */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
              <Award className="h-5 w-5 mr-2" />
              IELTS Test Results History
            </h2>

            {candidate.testResults && candidate.testResults.length > 0 ? (
              <div className="space-y-6">
                {candidate.testResults.map((result, index) => (
                  <div key={result.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Test Result #{index + 1}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Taken on {new Date(result.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(result.status)}`}>
                          {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                        </span>
                        {result.certificateGenerated && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-green-800 bg-green-100">
                            <Award className="h-3 w-3 mr-1" />
                            Certificate
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Band Scores */}
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Listening</div>
                        <div className={`text-lg font-semibold ${getBandScoreColor(result.listeningBandScore)}`}>
                          {result.listeningBandScore}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Reading</div>
                        <div className={`text-lg font-semibold ${getBandScoreColor(result.readingBandScore)}`}>
                          {result.readingBandScore}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Writing</div>
                        <div className={`text-lg font-semibold ${getBandScoreColor(result.writingBandScore)}`}>
                          {result.writingBandScore}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Speaking</div>
                        <div className={`text-lg font-semibold ${getBandScoreColor(result.speakingBandScore)}`}>
                          {result.speakingBandScore}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">Overall</div>
                        <div className={`text-xl font-bold ${getBandScoreColor(result.overallBandScore)}`}>
                          {result.overallBandScore}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        {result.aiFeedbackGenerated && (
                          <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                            AI Feedback Available
                          </div>
                        )}
                        {result.certificateSerial && (
                          <div className="flex items-center">
                            <Award className="h-4 w-4 mr-1 text-blue-500" />
                            Certificate: {result.certificateSerial}
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Link
                          href={`/results/${result.id}`}
                          className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View Details
                        </Link>
                        {result.certificateGenerated && (
                          <a
                            href={`/api/certificate/${result.id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Certificate
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">No test results found</p>
                <p className="text-gray-400 text-sm mt-2">
                  This candidate hasn't taken any IELTS tests yet
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, ilike, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Universal search - try all possible fields
    const results = await db
      .select({
        id: testResults.id,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        certificateGenerated: testResults.certificateGenerated,
        candidate: {
          fullName: candidates.fullName,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(
        or(
          // Search by full name (partial match)
          ilike(candidates.fullName, `%${query}%`),
          // Search by exact email
          eq(candidates.email, query),
          // Search by exact passport number
          eq(candidates.passportNumber, query),
          // Search by candidate number
          eq(candidates.candidateNumber, query),
          // Search by result ID
          eq(testResults.id, query),
          // Search by certificate serial
          eq(testResults.certificateSerial, query)
        )
      );

    return NextResponse.json(results);
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
